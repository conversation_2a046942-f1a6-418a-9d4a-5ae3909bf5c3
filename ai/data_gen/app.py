from dotenv import load_dotenv
load_dotenv()

from fastapi import FastAP<PERSON>, HTTPException, Form
from design.initial_data_gen import process_file
from product.initial_data_gen import process_product_file, process_product_feed
from datetime import datetime
import uvicorn
from pydantic import BaseModel
from design.global_variables import gda
from fastapi.middleware.cors import CORSMiddleware
import traceback
import os
import logging
import vertexai


# Ensure the environment variables are set
if not os.environ.get("GOOGLE_CLOUD_PROJECT"):
    raise EnvironmentError("GOOGLE_CLOUD_PROJECT environment variable is not set.")
if not os.environ.get("VERTEXAI_LOCATION"):
    raise EnvironmentError("VERTEXAI_LOCATION environment variable is not set.")

# Setup logging
logger = logging.getLogger("uvicorn.Info")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

PROJECT_ID = os.environ.get("GOOGLE_CLOUD_PROJECT")
LOCATION = os.environ.get("VERTEXAI_LOCATION", "us-central1")

# Initialize the LLM model once on startup
@app.on_event("startup")
async def startup_event():
    logger.info("Starting up the application...")
    # Any initialization code goes here.  For example:
    # from chat_agent import llm # If you want to initialize here.
    # llm.initialize() # Or whatever initialization your LLM requires

    if not PROJECT_ID:
        logger.error("GOOGLE_CLOUD_PROJECT environment variable not set.")
        # Consider raising an exception here to prevent the app from starting
        # if the project ID is absolutely essential.
        raise RuntimeError("GOOGLE_CLOUD_PROJECT environment variable must be set.")  # Raise an exception
    logger.info(f"Using project ID: {PROJECT_ID}")

    # Initialize Vertex AI
    vertexai.init(project=PROJECT_ID, location=LOCATION)
    
    logger.info("Application startup complete.")

@app.get('/health')
def health_check():
    return {'status': 'ok'}
    
class RequestDesignInput(BaseModel):
    designImage: str
    products: list
    designerId: str
    lifestyleImageId: str
    roomType: str 
    
@app.post('/generate_design_data')
async def process_design_image_api(request: RequestDesignInput):
    
    try:
        temp = request.model_dump()
        
        design_image= temp['designImage']
        designer_id = temp['designerId']
        lifestyle_image_id = temp['lifestyleImageId']
        room_type = temp['roomType']
        
        given_design_attributes = {}
        given_design_attributes[gda.products.value] = temp['products']
        given_design_attributes[gda.room_type.value] = room_type
        
        # You MUST await the call to process_file if it's an async function
        # If process_product_file is synchronous, you might need to use `run_in_threadpool`
        # for proper asynchronous behavior
        # Process the image
        result = await process_file(design_image, given_design_attributes)
        
        if result is None:
            logger.error("Error : Failed to process image.")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail= {'error': 'Failed to process image'})
        
        result["designerId"] = designer_id
        result["lifestyleImageId"] = lifestyle_image_id
        result['designDataProcessedAt'] = {"date" : datetime.now()}
        result['chatResponse'] = f'Here are design details I have extracted from the given image of {room_type}.'
        
        return result
    
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={'error': str(e)})

class RequestProductInput(BaseModel):
    supplierId : str
    skuId : str
    productImages: list

@app.post('/generate_product_data')
async def process_product_image_api(request: RequestProductInput):
    
    try:
        temp = request.model_dump()
        
        supplier_id = temp['supplierId']
        sku_id = temp['skuId']
        product_images_list= temp['productImages']
        
        # You MUST await the call to process_file if it's an async function
        # If process_product_file is synchronous, you might need to use `run_in_threadpool`
        # for proper asynchronous behavior
        # Process the image
        result = await process_product_file(product_images_list)
        
        if result is None:
            logger.error("Error : Failed to process image.")
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail= {'error': 'Failed to process image'})
        
        result["supplierId"] = supplier_id
        result["skuId"] = sku_id
        result['chatResponse'] = f'Here are product details I have extracted from the given images.'
        
        return result
    
    except Exception as e:
        logger.error(f"Error : {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={'error': str(e)})

class RequestProductFeedInput(BaseModel):
    productFeedInput : str
    
@app.post('/generate_for_product_data_list')
async def process_for_product_data_list_api(request: RequestProductFeedInput):
    try:
        temp = request.model_dump()
        
        product_feed_input = temp['productFeedInput']
        
        result, msg = await process_product_feed(product_feed_input)
        
        if not result:
            msg = f"Error : Failed to process product feed {product_feed_input}.\n\n{msg}."
            logger.error(traceback.format_exc())
            raise HTTPException(status_code=500, detail= {'error': msg})
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail={'error': str(e)})
    # try:
    #     temp = request.model_dump()
        
    #     supplier_id = temp['supplierId']
    #     sku_id = temp['skuId']
    #     product_images_list= temp['productImages']
        
    #     # You MUST await the call to process_file if it's an async function
    #     # If process_product_file is synchronous, you might need to use `run_in_threadpool`
    #     # for proper asynchronous behavior
    #     # Process the image
    #     result = await process_product_file(product_images_list)
        
    #     if result is None:
    #         logger.error("Error : Failed to process image.")
    #         logger.error(traceback.format_exc())
    #         raise HTTPException(status_code=500, detail= {'error': 'Failed to process image'})
        
    #     result["supplierId"] = supplier_id
    #     result["skuId"] = sku_id
    #     result['chatResponse'] = f'Here are product details I have extracted from the given images.'
        
    #     return result
    
    # except Exception as e:
    #     logger.error(f"Error : {str(e)}")
    #     logger.error(traceback.format_exc())
    #     raise HTTPException(status_code=500, detail={'error': str(e)})

if __name__ == "__main__":
        uvicorn.run("app:app", host="0.0.0.0", port=8088, reload=True)