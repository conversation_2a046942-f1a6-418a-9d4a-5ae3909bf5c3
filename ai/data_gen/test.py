import asyncio
from concurrent.futures import <PERSON><PERSON>ool<PERSON>xecutor
from time import sleep

def cpu_intensive_task(n):
    """A placeholder for a CPU-bound task."""
    print(n)
    sleep(n)
    print(n)
    result = 0
    for i in range(n * 1_000_000):
        result += i
    print(result)
    return result

async def main():
    loop = asyncio.get_running_loop()
    with Process<PERSON>oolExecutor() as executor:
        # Submit CPU-bound tasks to the process pool
        task1 = loop.run_in_executor(executor, cpu_intensive_task, 15)
        task2 = loop.run_in_executor(executor, cpu_intensive_task, 5)

        # Await the results
        result1 = await task1
        result2 = await task2

        print(f"Result from task 1: {result1}")
        print(f"Result from task 2: {result2}")

if __name__ == "__main__":
    asyncio.run(main())