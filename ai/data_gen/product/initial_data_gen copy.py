import os
from glob import glob
import re


from google.api_core.exceptions import ResourceExhausted
import time
import pprint
import io
import requests
pp = pprint.PrettyPrinter(depth=3)
import asyncio
from starlette.concurrency import run_in_threadpool
import logging
from urllib.parse import urlparse
from concurrent.futures import ProcessPoolExecutor

if __name__ == "__main__":
    from dal.product_queries import check_if_collection_exists, get_product_list, get_category_info, update_product_sku
else:
    from product.dal.product_queries import check_if_collection_exists, get_product_list, get_category_info, update_product_sku

# Setup logging
logger = logging.getLogger("uvicorn.Info")


if __name__ == "__main__":
    from global_variables import MAX_RETRIES, RETRY_DELAY_SECONDS
    from vertex_ai import get_vertex_ai_response
    from text_search.similarity import find_closest_match
else:
    from .global_variables import MAX_RETRIES, RETRY_DELAY_SECONDS
    from .vertex_ai import get_vertex_ai_response
    from .text_search.similarity import find_closest_match

def get_system_prompt():
    
    system_prompt = f"""
    You are a product data generator for a supplier. Your task is to analyze images and extract specific fields related to the product.

    Analyze the image and extract the following fields:

*   **subCategory:** Analyse the product from the images and choose the most appropriate sub category from the list below.:
Slat Beds
Panel Beds
Bunk Beds
Canopy Beds
Headboards
Beds
Dressers
Trunks
Nightstands
Storage Benches
Dining Chairs
Side and End Tables
High Tables
Bar Stools
Sectionals
Loveseats
Accent Rugs
Media and Entertainment
Poster Beds
Chests
Mirrors
Vanity Benches
Counter Chairs
Coffee Tables
Table Sets
Armchair and Ottoman Sets
Vanity Sets
Counter Stools
Daybeds
Runners
Shelves and Bookcases
Hall Trees
Kitchen Carts
Buffets
Storage Beds
Armchairs
Dining Tables
Console Tables
Office Sets
Stools
Area Rugs
Closet Organization
Dining Benches
Office Chairs
Recliners
Vanities
Desks
Sofas
Ottomans
File Cabinets
Cabinets
Kitchen Islands
Magazine Racks
Tabletop Accents
Vases
Lamps
Paintings
Pillows
Planters
Throw Blankets
Bars
Kitchen Island Sets
Corner Cabinets
Corner Chairs
Benches
Slipper Chairs
Servers
Pantries
Decorative Bowls
Screens
Sculptures
Accent Chair
Side Chairs
Cocktail Tables
Sofa Beds
Lounge Chairs
Bar Carts & Cabinets
SideBoards
Coat Racks
Platform Beds
Outdoor Rugs
Indoor Rugs
Indoor Rug Pads
Outdoor Rug Pads
Accent Stools
Accent Tables
Floor Lamps
Pendant Lamps
Table Lamps
Bar Tables
Rug Pads
Trestle Tables
Armless Chairs
Wingback Beds
Open Frame Beds
Trundle Beds
Chairs
Massage Chairs
Room Divider
Grandfather Clocks
Glass Tops
Racks
Tables
Loft Beds
Under Bed Storage
Game Tables
Footdboards
Dining Table Bases
Mattresses
Fireplaces
Swivel Chairs

You must only select one item for subCategory. 
*   **color:** The primary color(s) of the product (e.g., Red, Blue, Black). Give in comma separated values if multiple colors are present.
*   **style:** The aesthetic or design style of the product (e.g., Modern, Vintage, Minimalist). If a product has more than one style, give it in comma separated format else give only one style
*   **material:** The main material(s) used to construct the product (e.g., Wood, Metal, Cotton). Give in comma separated values if multiple materials are present.
*   **dimensions:** The size and measurements of the product (e.g., Height x Width x Depth). Always give measurements in inches. Give in key value pair format like height: 36", width: 48", depth: 12"
*   **shipmentRequired:** Give type of shipment required like parcel or full truckload (FTL) or less than truckload (LTL) shipping or similar. Give only single value without any details.
*   **title:** A concise and descriptive name for the product. strictly write within 20 words.
*   **romanceCopy:** It is called as ad copy. Give a short, engaging description highlighting the product's unique features and benefits.
*   **description:** A detailed explanation of the product's features, materials, and intended use.
*   **thingsUsedDuringManufacturing:** Components used in the product's manufacturing like quality of wood used or paints used etc. Give in full english sentence
*   **designPrinciplesFollowed:** The design principles or philosophies that guided the product's creation (e.g., Ergonomics, Sustainability). Give in full english sentence
*   **maintainanceInstructions:** Any specific instructions or recommendations for maintaining the product. Give in full english sentence


Extract the requested information based on the visual content of the image.

**Instructions:**
* Do not repeat words or phrases.
* Keep the response concise and to the point.
* Do not add unnecessary explanations.
    """
    
    return system_prompt

def get_system_prompt_22():
    
    system_prompt = f"""
    You are a product data generator for a supplier. Your task is to analyze images and extract specific fields related to the product.

    Analyze the image and extract the following fields:

*   **product_view_orientation:** Any one orientation like front, back, side, 45 degrees
Extract the requested information based on the visual content of the image.

**Instructions:**
* Do not repeat words or phrases.
* Keep the response concise and to the point.
* Do not add unnecessary explanations.
    """
    
    return system_prompt


def get_system_prompt_for_product_feed():
    
    system_prompt = f"""
    You are a product data generator for a supplier. Your task is to analyze images and extract specific fields related to the product.

    Analyze the image and extract the following fields:

*   **subCategory:** Analyse the product from the images and choose the most appropriate sub category from the list below.:
Slat Beds
Panel Beds
Bunk Beds
Canopy Beds
Headboards
Beds
Dressers
Trunks
Nightstands
Storage Benches
Dining Chairs
Side and End Tables
High Tables
Bar Stools
Sectionals
Loveseats
Accent Rugs
Media and Entertainment
Poster Beds
Chests
Mirrors
Vanity Benches
Counter Chairs
Coffee Tables
Table Sets
Armchair and Ottoman Sets
Vanity Sets
Counter Stools
Daybeds
Runners
Shelves and Bookcases
Hall Trees
Kitchen Carts
Buffets
Storage Beds
Armchairs
Dining Tables
Console Tables
Office Sets
Stools
Area Rugs
Closet Organization
Dining Benches
Office Chairs
Recliners
Vanities
Desks
Sofas
Ottomans
File Cabinets
Cabinets
Kitchen Islands
Magazine Racks
Tabletop Accents
Vases
Lamps
Paintings
Pillows
Planters
Throw Blankets
Bars
Kitchen Island Sets
Corner Cabinets
Corner Chairs
Benches
Slipper Chairs
Servers
Pantries
Decorative Bowls
Screens
Sculptures
Accent Chair
Side Chairs
Cocktail Tables
Sofa Beds
Lounge Chairs
Bar Carts & Cabinets
SideBoards
Coat Racks
Platform Beds
Outdoor Rugs
Indoor Rugs
Indoor Rug Pads
Outdoor Rug Pads
Accent Stools
Accent Tables
Floor Lamps
Pendant Lamps
Table Lamps
Bar Tables
Rug Pads
Trestle Tables
Armless Chairs
Wingback Beds
Open Frame Beds
Trundle Beds
Chairs
Massage Chairs
Room Divider
Grandfather Clocks
Glass Tops
Racks
Tables
Loft Beds
Under Bed Storage
Game Tables
Footdboards
Dining Table Bases
Mattresses
Fireplaces
Swivel Chairs

You must only select one item for subCategory. 

Extract the requested information based on the visual content of the image.

**Instructions:**
* Do not repeat words or phrases.
* Keep the response concise and to the point.
* Do not add unnecessary explanations.
    """
    
    return system_prompt

# %%
# **** TEMP SETTING FOR DEVELOPMENT : MADE TO CREATE TEST CASE FOR CHAT HISTORY ****
# def dict_to_string(res):
#     res_string = ""
#     for key, value in res.items():
#         if key == "features":
#             for sub_key, sub_value in value.items():
#                 res_string += f"{sub_key}: {sub_value}\n"
#         elif key == "productImageOrientationList":
#             pass
#         else:
#             res_string += f"*   **{key}**: {value}\n"
#     return res_string

# def list_to_string(res):
#     res_string = ""
#     counter = 0
#     for element in res:
#         counter += 1
#         for key, value in element.items():
#             res_string += f"*   **{key} {counter}**: {value}\n"
#     return res_string
# async def download_file_to_io(url):
#     retry_count = 0
#     while retry_count < MAX_RETRIES:
#         try:
#             response = requests.get(url)
#             response.raise_for_status()  # Raise an error for bad status
#             file_obj = io.BytesIO(response.content)
#             file_binary = file_obj.getvalue()
#             logger.info(f"Downloaded file from {url}")
#             print(f"Downloaded file from {url}")
#             return file_binary
#         except:
#             logger.error(f"Error downloading file from {url}", exc_info=True)
#             logger.warning(f"Retrying in {RETRY_DELAY_SECONDS} seconds... (Attempt {retry_count + 1}/{MAX_RETRIES})")
#             retry_count += 1
#             await asyncio.sleep(RETRY_DELAY_SECONDS)
#     return None

async def process_product_file(product_images_list):
    try:
        retry_count = 0
        
        # product_images_list_v2 = []
        
        # Download all images to IO        
        # for product_image in product_images_list:
        #     binary_file = await download_file_to_io(product_image)
        #     product_images_list_v2.append((product_image, binary_file))
        
        
        # start_time = time.time()
        
        # download_tasks = [download_file_to_io(product_image) for product_image in product_images_list]
        # binaries = await asyncio.gather(*download_tasks)
        
        # count_binary_None = 0
        # for i in range(len(product_images_list)):
        #     if binaries[i] is None:
        #         logger.error(f"Error downloading file from {product_images_list[i]}. Skipping this file")
        #         count_binary_None+= 1
        #         continue
        #     product_images_list_v2.append((product_images_list[i], binaries[i]))
        
        # if count_binary_None == len(product_images_list):
        #     raise Exception(f"Failed to download all files.")
        
        # # Timer end and log
        # end_time = time.time()   
        # logger.info(f"Downloaded {len(product_images_list_v2)}/{len(product_images_list)} images in {end_time - start_time:.2f} seconds.")    
        
        
        if len(product_images_list)  > 15:
            images_list = product_images_list[:15]
        else:
            images_list = product_images_list
    
        system_prompt = get_system_prompt()
        
        while retry_count < MAX_RETRIES:
            # **** TEMP SETTING FOR DEVELOPMENT : MADE TO CREATE TEST CASE FOR CHAT HISTORY ****
            # serializable_messages = []
            # serializable_messages.append({"type": "human", "content": system_prompt})
            try:
                # res = get_vertex_ai_response(product_images_list, system_prompt, response_schema = "initial_product_data_extract" ,model = 'flash')
                # Use run_in_threadpool for the synchronous Vertex AI call
                # This ensures the blocking operation happens in a separate thread,
                # freeing up the event loop.
                # Use a lambda function to capture the named arguments
                res = await run_in_threadpool(
                    lambda: get_vertex_ai_response(
                        images_list,
                        system_prompt,
                        response_schema="initial_product_data_extract", # Named argument
                        model='flash' # Named argument
                    )
                )

                # cat, sub_cat = find_closest_match(res['subCategory'])
                res_tuple = await run_in_threadpool(
                    lambda: find_closest_match(
                        res['subCategory']
                    )
                )
                cat, sub_cat = res_tuple
                logger.info(f'Category: {cat}, Sub Category: {sub_cat}')
                
                res['category'] = cat
                res['subCategory'] = sub_cat
                
                product_image_orientation_list = []
                for product_image in product_images_list_v2:
                    system_prompt_22 = get_system_prompt_22()
                    # res_22 = get_vertex_ai_response(product_image, system_prompt_22, response_schema = "initial_product_data_extract_22" ,model = 'flash')
                    # Use a lambda function again for the second call
                    res_22 = await run_in_threadpool(
                        lambda: get_vertex_ai_response(
                            product_image,
                            system_prompt_22,
                            response_schema="initial_product_data_extract_22", # Named argument
                            model='flash' # Named argument
                        )
                    )
                    
                    res_22['productImage'] = product_image[0]
                    # res[product_image] = res_22
                    product_image_orientation_list.append(res_22)
                res['productImageOrientationList'] = product_image_orientation_list
                
                # **** TEMP SETTING FOR DEVELOPMENT : MADE TO CREATE TEST CASE FOR CHAT HISTORY ****
                # # Devepoment Save to Json
                # res_string = dict_to_string(res)
                # res_string_22 = list_to_string(product_image_orientation_list)
                # serializable_messages.append({"type": "ai", "content": res_string})
                # import json
                # with open("chat_history_session.json", "w") as f:
                #     json.dump(serializable_messages, f, indent=2)
                
                return res
            except ResourceExhausted as e:
                # Handle 429 error (Rate Limit Exceeded)
                logger.warning(f"Rate limit exceeded (429 error). Retrying in {RETRY_DELAY_SECONDS} seconds... (Attempt {retry_count + 1}/{MAX_RETRIES})")
                retry_count += 1
                await asyncio.sleep(RETRY_DELAY_SECONDS)#time.sleep(RETRY_DELAY_SECONDS)
            except Exception as e:
                logger.error(f"Error processing image in product data extract: {e}", exc_info=True)
                return None
        logger.error(f"Max retries ({MAX_RETRIES}) exceeded for product data extraction.")
        return None # All retries failed
    
    except Exception as e:
        logger.error(f"Error processing file product data extract: {e}", exc_info=True)
        return None




async def get_product_type_and_subtype(product_images_list):
    try:
        retry_count = 0
        
        # product_images_list_v2 = []
        
        # Download all images to IO        
        # for product_image in product_images_list:
        #     binary_file = await download_file_to_io(product_image)
        #     product_images_list_v2.append((product_image, binary_file))
        
        
        # start_time = time.time()
        
        # download_tasks = [download_file_to_io(product_image) for product_image in product_images_list]
        # binaries = await asyncio.gather(*download_tasks)
        
        # count_binary_None = 0
        # for i in range(len(product_images_list)):
        #     if binaries[i] is None:
        #         logger.error(f"Error downloading file from {product_images_list[i]}. Skipping this file")
        #         count_binary_None+= 1
        #         continue
        #     product_images_list_v2.append((product_images_list[i], binaries[i]))
        
        # if count_binary_None == len(product_images_list):
        #     raise Exception(f"Failed to download all files.")
        
        # # Timer end and log
        # end_time = time.time()   
        # logger.info(f"Downloaded {len(product_images_list_v2)}/{len(product_images_list)} images in {end_time - start_time:.2f} seconds.")    
        
        
        if len(product_images_list)  > 15:
            images_list = product_images_list[:15]
        else:
            images_list = product_images_list
    
        system_prompt = get_system_prompt_for_product_feed()
        
        while retry_count < MAX_RETRIES:
            # **** TEMP SETTING FOR DEVELOPMENT : MADE TO CREATE TEST CASE FOR CHAT HISTORY ****
            # serializable_messages = []
            # serializable_messages.append({"type": "human", "content": system_prompt})
            try:
                # res = get_vertex_ai_response(product_images_list, system_prompt, response_schema = "initial_product_data_extract" ,model = 'flash')
                # Use run_in_threadpool for the synchronous Vertex AI call
                # This ensures the blocking operation happens in a separate thread,
                # freeing up the event loop.
                # Use a lambda function to capture the named arguments
                res = await run_in_threadpool(
                    lambda: get_vertex_ai_response(
                        images_list,
                        system_prompt,
                        response_schema="initial_product_data_extract_for_product_feed", # Named argument
                        model='flash' # Named argument
                    )
                )

                # cat, sub_cat = find_closest_match(res['subCategory'])
                res_tuple = await run_in_threadpool(
                    lambda: find_closest_match(
                        res['subCategory']
                    )
                )
                cat, sub_cat = res_tuple
                logger.info(f'Category: {cat}, Sub Category: {sub_cat}')
                
                res['category'] = cat
                res['subCategory'] = sub_cat
                
                return res
            except ResourceExhausted as e:
                # Handle 429 error (Rate Limit Exceeded)
                logger.warning(f"Rate limit exceeded (429 error). Retrying in {RETRY_DELAY_SECONDS} seconds... (Attempt {retry_count + 1}/{MAX_RETRIES})")
                retry_count += 1
                await asyncio.sleep(RETRY_DELAY_SECONDS)#time.sleep(RETRY_DELAY_SECONDS)
            except Exception as e:
                logger.error(f"Error processing image in product data extract: {e}", exc_info=True)
                return None
        logger.error(f"Max retries ({MAX_RETRIES}) exceeded for product data extraction.")
        return None # All retries failed
    
    except Exception as e:
        logger.error(f"Error processing file product data extract: {e}", exc_info=True)
        return None
def is_url(string):
    """
    Checks if a string is a valid URL.
    """
    try:
        result = urlparse(string)
        return all([result.scheme, result.netloc])
    except ValueError:
        return False

def get_image_url_list(product_sku):
    pattern = re.compile(r"image",re.IGNORECASE)
    pattern_link = re.compile(r"link|url",re.IGNORECASE)
    logger.info (f"Doing product SKU {product_sku['SKU']}")
    product_sku_keys = product_sku.keys()
    image_link_list = []
    for key in product_sku_keys:
        print(f'product_sku[{key}] {product_sku[key]}')
        if pattern.search(key) and pattern_link.search(key) and product_sku[key] is not None and is_url(str(product_sku[key])) and 'cloudfront.net' in str(product_sku[key]):
            image_link_list.append(product_sku[key])
    print(image_link_list)
    return image_link_list

def do_product_subproduct_type_process(product_feed_input, product):
    logger.info(f"Doing product {product['SKU']}")
    image_link_list = get_image_url_list(product)
    logger.info(f"Total image links found : {len(image_link_list)}")
    if len(image_link_list) == 0:
        logger.info(f"No image links found. Skipping product {product['SKU']}")
        return False
    res = get_product_type_and_subtype(image_link_list)
    print(res)
    res_product_type = get_category_info(category=res['category'],subCategory= res['subCategory'])
    print(res_product_type)
    res = update_product_sku(product_feed_input, product['SKU'], res_product_type)
    print(res_product_type)

async def process_product_feed(product_feed_input):
    if not await check_if_collection_exists(product_feed_input):
        msg = f"Collection {product_feed_input} does not exist."
        logger.error(msg)
        return False, msg
    logger.info(f"Collection {product_feed_input} exists.")
    product_list = await get_product_list(product_feed_input)
    logger.info(f"Total products got in collection {product_feed_input} : {len(product_list)}")
    loop = asyncio.get_running_loop()
    
    with ProcessPoolExecutor() as executor:
        # Create tasks for all values in the list
        tasks = [
            loop.run_in_executor(executor, do_product_subproduct_type_process, product_feed_input, product)
            for product in product_list[:2]
        ]
        
        # Await all results
        results = await asyncio.gather(*tasks)
        
        # Print results
        for i, result in enumerate(results):
            print(f"Result from task {i+1} (value {product_list[i]}): {result}")
        
        
    
    
    
    


'''
###################################### ALL DEBUG and DEVELOPMENT TESING is below ###################################### 
'''



# # *************** SIMPLE DEBUG *********************

if __name__ == "__main__":
    asyncio.run(process_product_feed("productFeedRaw- OW-Ingestion2"))
# if __name__ == "__main__":
#     product_images_list =[
#         # "gs://designdatageneration/92001-08-2PC SOFA/92001-08-2PC SOFA_1.jpg",
#         # "gs://designdatageneration/92001-08-2PC SOFA/92001-08-2PC SOFA_DIM.jpeg"
#         # r"https://nestingale.com/_next/image?url=https%3A%2F%2Fd221a61rb87vel.cloudfront.net%2Fcatalog-assets%2Fproducts%2Fcomfortpointe%2F93005-08-2PC_SOFA%2F93005-08-2PC_SOFA_main.jpg&w=1920&q=75",
#         # r"https://nestingale.com/_next/image?url=https%3A%2F%2Fd221a61rb87vel.cloudfront.net%2Fcatalog-assets%2Fproducts%2Fcomfortpointe%2F93005-08-2PC_SOFA%2F93005-08-2PC_SOFA_1.jpg&w=1920&q=75",
#         # "gs://designdatageneration/test/93005-08-2PC_SOFA_1.webp",
#         # "gs://designdatageneration/test/93005-08-2PC_SOFA_main.webp"
#         # "https://d221a61rb87vel.cloudfront.net/designImages/fc525933-a90b-4029-98e3-643e4e11afce_EPS213/d0190fc9-b1e6-48c0-86db-3ae342762eb2.png"
#         "https://d221a61rb87vel.cloudfront.net/designImages/54c844d8-5031-707f-3c21-5c00659503d9_T23730/15ac6baf-6926-4a8c-a60c-299b746bf7b1.png",
#         "https://d221a61rb87vel.cloudfront.net/designImages/54c844d8-5031-707f-3c21-5c00659503d9_T23730/e4fc5904-eb78-46a7-b46c-61d3ac8a5d04.png"
#     ]
#     res = asyncio.run(process_product_file(product_images_list))
#     pp.pprint(res)
    
#     # **** TEMP SETTING FOR DEVELOPMENT : MADE FOR UDAY DB CALL REPLACEMENT ****
#     # import json
#     # with open("supplierId_skuId.json", "w") as f:
#     #     json.dump(res, f, indent=2)



# # *************** PARALLEL PROCESSING DEBUG *********************
# def do_work():
#     product_images_list =[
#         # "gs://designdatageneration/92001-08-2PC SOFA/92001-08-2PC SOFA_1.jpg",
#         # "gs://designdatageneration/92001-08-2PC SOFA/92001-08-2PC SOFA_DIM.jpeg"
#         r"https://nestingale.com/_next/image?url=https%3A%2F%2Fd221a61rb87vel.cloudfront.net%2Fcatalog-assets%2Fproducts%2Fcomfortpointe%2F93005-08-2PC_SOFA%2F93005-08-2PC_SOFA_main.jpg&w=1920&q=75",
#         r"https://nestingale.com/_next/image?url=https%3A%2F%2Fd221a61rb87vel.cloudfront.net%2Fcatalog-assets%2Fproducts%2Fcomfortpointe%2F93005-08-2PC_SOFA%2F93005-08-2PC_SOFA_1.jpg&w=1920&q=75",
#         # "gs://designdatageneration/test/93005-08-2PC_SOFA_1.webp",
#         # "gs://designdatageneration/test/93005-08-2PC_SOFA_main.webp"
#         # "https://d221a61rb87vel.cloudfront.net/designImages/f67dcdb5-7806-4063-ae08-18127a83eddf_7777/d1e55fd0-7982-42de-bfdb-fa2d374f456c.png"
#     ]
#     res = process_product_file(product_images_list)
#     return str(res)

# def do_work_wrapper(_):
#     return do_work()

# from concurrent.futures import ProcessPoolExecutor

# def run_parallel_do_work():
#     with ProcessPoolExecutor() as executor:
#         # Call do_work 10 times in parallel
#         results = list(executor.map(do_work_wrapper, range(10)))
#     for indx,res in enumerate(results):
#         print(f"\n\nResult ******** {indx}  ********:")
#         print(res)

# if __name__ == "__main__":
#     run_parallel_do_work()