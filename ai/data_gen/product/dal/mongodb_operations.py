"""
Asynchronous MongoDB Operations Module
Provides functionality for connecting to MongoDB and performing CRUD operations asynchronously.
"""

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo.errors import ConnectionFailure, PyMongoError
from bson import ObjectId
from datetime import datetime
import logging
import asyncio
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MongoDBConnector:
    """
    A class to handle asynchronous MongoDB connections and operations.
    """
    
    def __init__(self, connection_uri: str, database_name: str = "test"):
        """
        Initialize MongoDB connector.
        
        Args:
            connection_uri (str): MongoDB connection URI
            database_name (str): Name of the database to use
        """
        self.connection_uri = connection_uri
        self.database_name = database_name
        self.client = AsyncIOMotorClient(self.connection_uri)
        self.db = self.client[self.database_name]
        
    async def connect(self):
        """
        Establish an asynchronous connection to MongoDB.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.client = AsyncIOMotorClient(self.connection_uri)
            # Test the connection
            await self.client.admin.command('ping')
            # self.db = self.client[self.database_name]
            logger.info(f"Successfully connected to MongoDB database: {self.database_name}")
            return True
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during connection: {e}")
            return False
    
    def disconnect(self):
        """
        Close MongoDB connection.
        """
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")
    
    async def get_records(self, collection_name: str, filter_query: dict = None, limit: int = None):
        """
        Asynchronously retrieve records from a collection.
        
        Args:
            collection_name (str): Name of the collection
            filter_query (dict): Query filter (optional)
            limit (int): Maximum number of records to return (optional)
            
        Returns:
            list: List of documents or empty list if error
        """
        try:
            collection = self.db[collection_name]
            
            if filter_query is None:
                filter_query = {}
            
            cursor = collection.find(filter_query)
            
            if limit:
                cursor = cursor.limit(limit)
            
            records = await cursor.to_list(length=limit)
            logger.info(f"Retrieved {len(records)} records from {collection_name}")
            return records
            
        except PyMongoError as e:
            logger.error(f"Error retrieving records from {collection_name}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error during get operation: {e}")
            return []
    
    async def insert_record(self, collection_name: str, document: dict):
        """
        Asynchronously insert a single record into a collection.
        
        Args:
            collection_name (str): Name of the collection
            document (dict): Document to insert
            
        Returns:
            str: Inserted document ID or None if error
        """
        try:
            collection = self.db[collection_name]
            
            # Add timestamp if not present
            if 'created_at' not in document:
                document['created_at'] = datetime.utcnow()
            
            result = await collection.insert_one(document)
            logger.info(f"Inserted record with ID: {result.inserted_id}")
            return str(result.inserted_id)
            
        except PyMongoError as e:
            logger.error(f"Error inserting record into {collection_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error during insert operation: {e}")
            return None
    
    async def insert_multiple_records(self, collection_name: str, documents: list):
        """
        Asynchronously insert multiple records into a collection.
        
        Args:
            collection_name (str): Name of the collection
            documents (list): List of documents to insert
            
        Returns:
            list: List of inserted document IDs or empty list if error
        """
        try:
            collection = self.db[collection_name]
            
            # Add timestamps if not present
            for doc in documents:
                if 'created_at' not in doc:
                    doc['created_at'] = datetime.utcnow()
            
            result = await collection.insert_many(documents)
            inserted_ids = [str(id) for id in result.inserted_ids]
            logger.info(f"Inserted {len(inserted_ids)} records")
            return inserted_ids
            
        except PyMongoError as e:
            logger.error(f"Error inserting multiple records into {collection_name}: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error during bulk insert operation: {e}")
            return []
    
    async def update_record(self, collection_name: str, filter_query: dict, update_data: dict, upsert: bool = False):
        """
        Asynchronously update a single record in a collection.
        
        Args:
            collection_name (str): Name of the collection
            filter_query (dict): Query to find the document to update
            update_data (dict): Data to update
            upsert (bool): Create document if it doesn't exist
            
        Returns:
            dict: Update result information
        """
        try:
            collection = self.db[collection_name]
            
            # Add update timestamp
            if '$set' not in update_data:
                update_data = {'$set': update_data}
            
            if '$set' in update_data:
                update_data['$set']['updated_at'] = datetime.utcnow()
            
            result = await collection.update_one(filter_query, update_data, upsert=upsert)
            
            update_info = {
                'matched_count': result.matched_count,
                'modified_count': result.modified_count,
                'upserted_id': str(result.upserted_id) if result.upserted_id else None
            }
            
            logger.info(f"Update operation completed: {update_info}")
            return update_info
            
        except PyMongoError as e:
            logger.error(f"Error updating record in {collection_name}: {e}")
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error during update operation: {e}")
            return {'error': str(e)}
    
    async def update_multiple_records(self, collection_name: str, filter_query: dict, update_data: dict):
        """
        Asynchronously update multiple records in a collection.
        
        Args:
            collection_name (str): Name of the collection
            filter_query (dict): Query to find documents to update
            update_data (dict): Data to update
            
        Returns:
            dict: Update result information
        """
        try:
            collection = self.db[collection_name]
            
            # Add update timestamp
            if '$set' not in update_data:
                update_data = {'$set': update_data}
            
            if '$set' in update_data:
                update_data['$set']['updated_at'] = datetime.utcnow()
            
            result = await collection.update_many(filter_query, update_data)
            
            update_info = {
                'matched_count': result.matched_count,
                'modified_count': result.modified_count
            }
            
            logger.info(f"Bulk update operation completed: {update_info}")
            return update_info
            
        except PyMongoError as e:
            logger.error(f"Error updating multiple records in {collection_name}: {e}")
            return {'error': str(e)}
        except Exception as e:
            logger.error(f"Unexpected error during bulk update operation: {e}")
            return {'error': str(e)}
    
    async def delete_record(self, collection_name: str, filter_query: dict):
        """
        Asynchronously delete a single record from a collection.
        
        Args:
            collection_name (str): Name of the collection
            filter_query (dict): Query to find the document to delete
            
        Returns:
            int: Number of deleted documents
        """
        try:
            collection = self.db[collection_name]
            result = await collection.delete_one(filter_query)
            logger.info(f"Deleted {result.deleted_count} record(s)")
            return result.deleted_count
            
        except PyMongoError as e:
            logger.error(f"Error deleting record from {collection_name}: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error during delete operation: {e}")
            return 0
    
    async def get_collection_stats(self, collection_name: str):
        """
        Asynchronously get statistics about a collection.
        
        Args:
            collection_name (str): Name of the collection
            
        Returns:
            dict: Collection statistics
        """
        try:
            stats = await self.db.command("collStats", collection_name)
            return {
                'document_count': stats.get('count', 0),
                'size_bytes': stats.get('size', 0),
                'average_object_size': stats.get('avgObjSize', 0)
            }
        except PyMongoError as e:
            logger.error(f"Error getting stats for {collection_name}: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error getting collection stats: {e}")
            return {}


async def main():
    """
    Example usage of the asynchronous MongoDB connector.
    """
    # Connection URI (replace with your actual credentials or use environment variables)
    CONNECTION_URI = os.environ.get("MONGO_CONNECTION_URI", "mongodb+srv://username:<EMAIL>/test")
    
    # Initialize connector
    mongo_connector = MongoDBConnector(CONNECTION_URI, "test")
    
    try:
        # Connect to MongoDB
        if not await mongo_connector.connect():
            print("Failed to connect to MongoDB")
            return
        
        # Collection name for our dummy data
        collection_name = "dummy_collection"
        
        print("=== Asynchronous MongoDB Operations Demo ===\n")
        
        # 1. Insert sample records
        print("1. Inserting sample records...")
        sample_records = [
            {"name": "John Doe", "age": 30, "city": "New York", "occupation": "Engineer"},
            {"name": "Jane Smith", "age": 25, "city": "Los Angeles", "occupation": "Designer"},
            {"name": "Bob Johnson", "age": 35, "city": "Chicago", "occupation": "Manager"}
        ]
        
        inserted_ids = await mongo_connector.insert_multiple_records(collection_name, sample_records)
        print(f"Inserted records with IDs: {inserted_ids}\n")
        
        # 2. Get all records
        print("2. Retrieving all records...")
        all_records = await mongo_connector.get_records(collection_name)
        for record in all_records:
            print(f"ID: {record['_id']}, Name: {record['name']}, Age: {record['age']}, City: {record['city']}")
        print()
        
        # 3. Get filtered records
        print("3. Retrieving records with age > 30...")
        filtered_records = await mongo_connector.get_records(collection_name, {"age": {"$gt": 30}})
        for record in filtered_records:
            print(f"Name: {record['name']}, Age: {record['age']}")
        print()
        
        # 4. Update a record
        print("4. Updating John Doe's age to 31...")
        update_result = await mongo_connector.update_record(
            collection_name,
            {"name": "John Doe"},
            {"age": 31, "status": "updated"}
        )
        print(f"Update result: {update_result}\n")
        
        # 5. Insert a single record
        print("5. Inserting a new single record...")
        new_record = {"name": "Alice Brown", "age": 28, "city": "Seattle", "occupation": "Developer"}
        new_id = await mongo_connector.insert_record(collection_name, new_record)
        print(f"Inserted new record with ID: {new_id}\n")
        
        # 6. Update multiple records
        print("6. Updating all records in Chicago...")
        bulk_update_result = await mongo_connector.update_multiple_records(
            collection_name,
            {"city": "Chicago"},
            {"status": "bulk_updated", "region": "Midwest"}
        )
        print(f"Bulk update result: {bulk_update_result}\n")
        
        # 7. Get final state of all records
        print("7. Final state of all records...")
        final_records = await mongo_connector.get_records(collection_name)
        for record in final_records:
            print(f"Name: {record['name']}, Age: {record['age']}, City: {record['city']}, Status: {record.get('status', 'N/A')}")
        print()
        
        # 8. Collection statistics
        print("8. Collection statistics...")
        stats = await mongo_connector.get_collection_stats(collection_name)
        print(f"Document count: {stats.get('document_count', 0)}")
        print(f"Collection size: {stats.get('size_bytes', 0)} bytes")
        
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        # Always disconnect
        mongo_connector.disconnect()


if __name__ == "__main__":
    asyncio.run(main())