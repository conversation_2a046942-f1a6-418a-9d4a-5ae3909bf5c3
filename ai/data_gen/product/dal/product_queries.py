if __name__ == "__main__":
    from .mongodb_operations import MongoDBConnector
    from ..global_variables import MongoDBConfig
else:
    # from product.dal.mongodb_operations import MongoDBConnector
    # from product.global_variables import MongoDBConfig
    
    from dal.mongodb_operations import MongoDBConnector
    from global_variables import MongoDBConfig
import sys
import re
import json
import asyncio
from dotenv import load_dotenv
load_dotenv()

import os
os.environ["GOOGLE_CLOUD_PROJECT"] = os.getenv("GOOGLE_CLOUD_PROJECT","lifestyle-design-generation")
os.environ["VERTEXAI_LOCATION"]  = os.getenv("VERTEXAI_LOCATION", "us-central1")


# Initialize MongoDB connector
mongo_connector = MongoDBConnector(MongoDBConfig.CONNECTION_URI, MongoDBConfig.DATABASE_NAME)

# Connect to MongoDB
if not asyncio.run(mongo_connector.connect()):
    print("❌ Failed to connect to MongoDB. Please check your connection URI and credentials.")
    sys.exit(1)

print("✅ Successfully connected to MongoDB!")

# # Check if collection exists
# collection_names = db.list_collection_names()

async def check_if_collection_exists(collection_name: str):

    # List all collections
    print("checking collections in database")
    return collection_name in await mongo_connector.db.list_collection_names()

async def get_product_list(collection_name: str):
    print(f"getting all product list in collection {collection_name}")
    return await mongo_connector.get_records(collection_name)

def get_image_url_list(collection_name: str):
    print(f"getting all image url list in collection {collection_name}")
    res = check_if_collection_exists(collection_name)
    if not res:
        print("collection not in database")
    
    res = product_list(collection_name)
    
    pattern = re.compile(r"image",re.IGNORECASE)
    pattern_link = re.compile(r"link|url",re.IGNORECASE)
    final_product_found = []
    final_product_matched_image = []
    for indx,product in enumerate(res[:10]):
        print(f"doing product {indx}")
        product_keys = product.keys()
        matching_strings_dict = {}
        for key in product_keys:
            if pattern.search(key):
                if pattern_link.search(key):
                    matching_strings_dict[key] = product[key]
        
        if matching_strings_dict != {}:
            final_product_found.append(product)
            final_product_matched_image.append(matching_strings_dict)
    return final_product_found, final_product_matched_image
        
async def get_category_info(category: str, subCategory: str):
    collection_name = 'ProductTypeMasterData'
    filter_query = {}
    filter_query['productTypeDisplayName'] = category
    filter_query['productSubTypeDisplayName'] = subCategory
    return await mongo_connector.get_records(collection_name, filter_query)

async def update_product_sku(collection_name: str, product_sku: str, update_data: dict):
    filter_query = {"SKU":product_sku}
    result = await mongo_connector.update_record(collection_name, filter_query, {"product_type":update_data})
    print(result)
    
if __name__ == "__main__":
    all_collection_names = mongo_connector.db.list_collection_names()
    
    collection_name_productFeedRaw = []
    for pattern in ['productFeedRaw', 'productFeedraw']:
        for collection_name in all_collection_names:
            if pattern in collection_name:
                collection_name_productFeedRaw.append(collection_name)            
    
    
    
    result_dict = {}
    result_dict_matched_image = {}
    # for collection_name in ['productFeedRaw - OW- Ingestion5']:
    for collection_name in collection_name_productFeedRaw:
        final_product_found, final_product_matched_image = get_image_url_list(collection_name)
        
        if len(final_product_found) == 0:
            continue
        
        # result_dict[collection_name] = final_product_found
        result_dict_matched_image[collection_name] = final_product_matched_image
    
    
    
    product_image_list = [
        "https://s3.us-east-2.amazonaws.com/portal.image.production/organization_GTR/78a4c072-d97d-491c-a85d-12ead5520008.jpg",
     "https://s3.us-east-2.amazonaws.com/portal.image.production/organization_GTR/5db5153d-f54e-4785-ac1b-0585f75296e4.jpg",
     "https://s3.us-east-2.amazonaws.com/portal.image.production/organization_GTR/fe9b9910-bfce-4247-b578-450e675c6eb4.jpg",
     "https://s3.us-east-2.amazonaws.com/portal.image.production/organization_GTR/133741c0-5978-4912-afab-8f902fb6b489.jpg"
     ]
    
    res = asyncio.run(process_product_feed(product_image_list))
    print(f'res: {res}')
    
    
    
    # # Specify the output file path
    # output_file = 'data_of_product_matched_image.json'
    # # Write the dictionary to a JSON file
    # with open(output_file, 'w') as json_file:
    #     # Use indent for pretty-printing the JSON
    #     json.dump(result_dict_matched_image, json_file, indent=4)