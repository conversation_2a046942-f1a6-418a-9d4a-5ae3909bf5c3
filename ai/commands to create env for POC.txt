conda init
conda create -n data_generate python=3.12 ipykernel -y
conda activate data_generate

sudo apt-get update;sudo apt update;sudo apt-get install aria2;cd "/home/<USER>/";aria2c -x 16 https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-cli-linux-x86_64.tar.gz;tar -xf google-cloud-cli-linux-x86_64.tar.gz;source '/home/<USER>/google-cloud-sdk/path.bash.inc';source '/home/<USER>/google-cloud-sdk/completion.bash.inc';gcloud auth application-default login --no-browser

pip install fastapi uvicorn python-multipart langchain langgraph python-dotenv google-cloud-aiplatform[agent_engines,langchain]==1.92.0 langchain_google_vertexai pydantic vertexai starlette firebase-admin aiofiles google-cloud-firestore langchain-google-vertexai sentence-transformers google-cloud-aiplatform motor
uvicorn app:app --host 0.0.0.0 --port 8088 --reload

****ngrok install 
curl -sSL https://ngrok-agent.s3.amazonaws.com/ngrok.asc   | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null   && echo "deb https://ngrok-agent.s3.amazonaws.com buster main"   | sudo tee /etc/apt/sources.list.d/ngrok.list   && sudo apt update   && sudo apt install ngrok
ngrok http http://localhost:8088